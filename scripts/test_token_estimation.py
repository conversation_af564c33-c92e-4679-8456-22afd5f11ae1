#!/usr/bin/env python3
"""
Token estimation validation script
测试改进的token估算算法的准确性
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dc_ai_red_line_review.utils import (
    get_token_count,
    validate_token_estimation,
    _estimate_qwen3_tokens,
    _estimate_qwen3_tokens_improved,
    analyze_text_languages,
    get_logger,
)


def test_token_estimation():
    """测试不同类型文本的token估算准确性"""
    logger = get_logger("token_test")

    # 测试用例 - 增加多语言支持测试
    test_cases = [
        {
            "name": "纯中文",
            "text": "您好，我是客服小助手，很高兴为您服务。请问有什么可以帮助您的吗？",
        },
        {
            "name": "纯英文",
            "text": "Hello, I am a customer service assistant. How can I help you today?",
        },
        {
            "name": "日语",
            "text": "こんにちは、カスタマーサービスです。どのようなご用件でしょうか？",
        },
        {"name": "韩语", "text": "안녕하세요, 고객 서비스입니다. 무엇을 도와드릴까요?"},
        {
            "name": "阿拉伯语",
            "text": "مرحبا، أنا مساعد خدمة العملاء. كيف يمكنني مساعدتك اليوم؟",
        },
        {
            "name": "俄语",
            "text": "Здравствуйте, я помощник службы поддержки. Как я могу вам помочь?",
        },
        {
            "name": "中英混合",
            "text": "您好！Welcome to our service. 请问您需要什么帮助？How can I assist you?",
        },
        {
            "name": "多语言混合",
            "text": "Hello 您好 こんにちは 안녕하세요 مرحبا Здравствуйте! Welcome to our international service.",
        },
        {
            "name": "包含特殊token",
            "text": "<|im_start|>user\n您好，请问如何申请退款？<|im_end|><|im_start|>assistant\n您好！关于退款申请，请提供您的订单号。<|im_end|>",
        },
        {
            "name": "包含数字和标点",
            "text": "订单号：12345678，金额：￥299.99，时间：2024-01-15 14:30:25。请确认信息是否正确？",
        },
        {"name": "长文本", "text": "尊敬的客户，感谢您选择我们的服务。" * 50},
    ]

    print("🧪 Token估算测试")
    print("=" * 80)

    for i, case in enumerate(test_cases, 1):
        print(f"\n📝 测试用例 {i}: {case['name']}")
        print(f"文本长度: {len(case['text'])} 字符")
        print(f"文本预览: {case['text'][:50]}{'...' if len(case['text']) > 50 else ''}")

        # 获取估算结果
        simple_tokens = _estimate_qwen3_tokens(len(case["text"]))
        improved_tokens = _estimate_qwen3_tokens_improved(case["text"])

        print(f"简单估算: {simple_tokens} tokens")
        print(f"改进估算: {improved_tokens} tokens")
        print(f"差异: {abs(improved_tokens - simple_tokens)} tokens")
        print(
            f"改进比例: {improved_tokens / simple_tokens:.2f}x"
            if simple_tokens > 0
            else "N/A"
        )

        # 语言分析
        lang_analysis = analyze_text_languages(case["text"])
        print(f"主要语言: {lang_analysis.get('primary_language', 'unknown')}")

        # 显示语言组成（只显示非零的）
        lang_info = []
        for lang, data in lang_analysis.get("languages", {}).items():
            if lang == "english" and data.get("words", 0) > 0:
                lang_info.append(f"{lang}: {data['words']} words")
            elif data.get("chars", 0) > 0:
                lang_info.append(
                    f"{lang}: {data['chars']} chars ({data['percentage']:.1f}%)"
                )

        if lang_info:
            print(f"语言组成: {', '.join(lang_info)}")

        # 验证结果
        validation = validate_token_estimation(case["text"], logger=logger)

        print("-" * 40)


def test_chunking_consistency():
    """测试分块逻辑的一致性"""
    from dc_ai_red_line_review.utils import ChonkieTextChunker

    print("\n🔧 分块一致性测试")
    print("=" * 80)

    # 创建测试文本
    test_text = (
        """
    <|im_start|>user
    您好，我想咨询一下关于产品退换货的政策。我在上个月购买了一款手机，但是发现有质量问题。
    <|im_end|>
    <|im_start|>assistant  
    您好！很抱歉听到您遇到的问题。关于退换货政策，我们支持7天无理由退货，15天质量问题换货。
    请您提供订单号，我来帮您查询具体情况。
    <|im_end|>
    <|im_start|>user
    我的订单号是：ORD20240115001234，购买时间是1月15日，商品是iPhone 15 Pro Max 256GB。
    问题是屏幕有亮点，影响使用体验。
    <|im_end|>
    """
        * 10
    )  # 重复10次创建长文本

    chunker = ChonkieTextChunker(chunk_size=1000, chunk_overlap=200)

    print(f"原始文本长度: {len(test_text)} 字符")
    print(f"估算token数: {get_token_count(test_text)} tokens")

    # 测试文本分块
    chunks, chunk_tokens = chunker.chunk_text(test_text)

    print(f"\n分块结果:")
    print(f"分块数量: {len(chunks)}")
    print(f"总token数: {sum(chunk_tokens)}")

    for i, (chunk, tokens) in enumerate(zip(chunks, chunk_tokens)):
        print(f"  分块 {i + 1}: {len(chunk)} 字符, {tokens} tokens")

        # 验证token估算一致性
        recalc_tokens = get_token_count(chunk)
        if abs(recalc_tokens - tokens) > 10:  # 允许小误差
            print(f"    ⚠️ Token估算不一致: 原始={tokens}, 重算={recalc_tokens}")


def main():
    """主函数"""
    print("🚀 Token估算优化验证")
    print("=" * 80)

    try:
        test_token_estimation()
        test_chunking_consistency()

        print("\n✅ 测试完成！")
        print("\n💡 使用建议:")
        print("1. 在生产环境中使用改进的token估算 (use_improved=True)")
        print("2. 定期使用validate_token_estimation()验证准确性")
        print("3. 根据实际使用情况调整估算参数")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
