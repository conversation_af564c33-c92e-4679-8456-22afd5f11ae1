import functools
import inspect
import logging
import os
import time

# Removed chonkie dependency - using local estimation
from dotenv import load_dotenv
from rich.console import Console
from rich.logging import Rich<PERSON><PERSON>ler

load_dotenv(override=True)

# Dictionary to track configured loggers
_CONFIGURED_LOGGERS = {}


def get_logger(module_name: str) -> logging.Logger:
    """Configure and return a module-specific logger instance.

    Args:
        module_name: The name of the module to identify in logs

    Returns:
        logging.Logger: A configured logger with rich formatting
    """
    # Check if this logger was already configured
    if module_name in _CONFIGURED_LOGGERS:
        return _CONFIGURED_LOGGERS[module_name]

    # Create module-specific logs directory
    LOG_DIR = os.environ["LOG_PATH"]

    # Create a custom logger
    custom_logger = logging.getLogger(module_name)

    # Prevent log propagation to avoid duplicate logs
    custom_logger.propagate = False

    # Clear any existing handlers to avoid duplicates
    if custom_logger.hasHandlers():
        custom_logger.handlers.clear()

    # Set the level
    custom_logger.setLevel(logging.INFO)

    # Configure console output with Rich
    console_handler = RichHandler(
        rich_tracebacks=True,
        console=Console(stderr=False),
        tracebacks_show_locals=True,
        show_time=True,
        show_path=True,
    )
    console_handler.setLevel(logging.INFO)
    custom_logger.addHandler(console_handler)

    # Configure file handler for log files
    try:
        os.makedirs(LOG_DIR, exist_ok=True)
        file_handler = logging.FileHandler(f"{LOG_DIR}/ai.log", encoding="utf-8")
        file_format = logging.Formatter(
            "{asctime} | {levelname:<8} | "
            + module_name
            + " | {name}:{funcName}:{lineno} - {message}",
            style="{",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        file_handler.setFormatter(file_format)
        file_handler.setLevel(logging.INFO)
        custom_logger.addHandler(file_handler)
    except Exception as e:
        console = Console()
        console.print(f"[bold red]Failed to set up file logging: {e}[/bold red]")

    # Store the configured logger
    _CONFIGURED_LOGGERS[module_name] = custom_logger

    return custom_logger


def get_token_count(text: str, use_improved: bool = True) -> int:
    """Estimate token count for Qwen3 model using local calculation.

    Args:
        text: Text to count tokens for
        use_improved: Whether to use improved language-aware estimation (default: True)

    Returns:
        int: Estimated token count
    """
    if not text:
        return 0

    if use_improved:
        return _estimate_qwen3_tokens_improved(text)
    else:
        return _estimate_qwen3_tokens(len(text))


def _estimate_qwen3_tokens(char_count: int) -> int:
    """Estimate token count for Qwen3 model based on character count.

    Based on empirical observations for Qwen models:
    - Chinese characters: ~2.5 chars per token
    - English words: ~4 chars per token
    - Special tokens (<|im_start|>, etc.): ~1 token each
    - Mixed content: ~3 chars per token (conservative estimate)

    This estimation is designed to be slightly conservative (overestimate)
    to ensure chunks don't exceed limits.
    """
    if char_count == 0:
        return 0

    # Conservative estimation: 3 chars per token for mixed content
    # This tends to slightly overestimate, which is safer for chunk size control
    estimated_tokens = max(1, char_count // 3)

    # Add small buffer for special tokens and formatting
    return int(estimated_tokens * 1.1)


def _estimate_qwen3_tokens_improved(text: str) -> int:
    """Improved token estimation for Qwen3 model with language-aware calculation.

    This function provides more accurate estimation by considering:
    - Different character types (Chinese, English, numbers, punctuation)
    - Special tokens used in chat formatting
    - Conservative estimation to prevent token limit exceeded

    Args:
        text: Input text to estimate tokens for

    Returns:
        int: Estimated token count
    """
    if not text:
        return 0

    import re

    # Count special tokens first
    special_tokens = len(re.findall(r"<\|im_start\|>|<\|im_end\|>", text))

    # Remove special tokens from text for character analysis
    clean_text = re.sub(r"<\|im_start\|>|<\|im_end\|>", "", text)

    # Count different character types
    chinese_chars = len(re.findall(r"[\u4e00-\u9fff]", clean_text))
    english_words = len(re.findall(r"[a-zA-Z]+", clean_text))
    numbers = len(re.findall(r"\d+", clean_text))
    punctuation = len(re.findall(r"[^\w\s\u4e00-\u9fff]", clean_text))

    # Calculate tokens based on empirical ratios
    chinese_tokens = chinese_chars / 2.5  # ~2.5 chars per token
    english_tokens = (
        english_words * 1.3
    )  # ~1.3 tokens per word (accounting for subwords)
    number_tokens = numbers * 0.8  # Numbers are often single tokens
    punct_tokens = punctuation * 0.5  # Punctuation often shares tokens

    # Sum up all token estimates
    estimated_tokens = (
        special_tokens  # Special tokens count as 1 each
        + chinese_tokens
        + english_tokens
        + number_tokens
        + punct_tokens
    )

    # Add 15% buffer for safety and round up
    return max(1, int(estimated_tokens * 1.15))


def validate_token_estimation(
    text: str, actual_tokens: int = None, logger=None
) -> dict:
    """Validate token estimation accuracy against actual token count.

    This function is useful for development and testing to verify estimation accuracy.

    Args:
        text: Text to analyze
        actual_tokens: Actual token count from API or tiktoken (optional)
        logger: Logger instance for recording results (optional)

    Returns:
        dict: Validation results including estimates and accuracy metrics
    """
    if not text:
        return {"error": "Empty text provided"}

    # Get estimates from both methods
    simple_estimate = _estimate_qwen3_tokens(len(text))
    improved_estimate = _estimate_qwen3_tokens_improved(text)

    result = {
        "text_length": len(text),
        "simple_estimate": simple_estimate,
        "improved_estimate": improved_estimate,
        "difference": abs(improved_estimate - simple_estimate),
        "improvement_ratio": improved_estimate / simple_estimate
        if simple_estimate > 0
        else 0,
    }

    if actual_tokens is not None:
        result.update(
            {
                "actual_tokens": actual_tokens,
                "simple_accuracy": abs(simple_estimate - actual_tokens) / actual_tokens
                if actual_tokens > 0
                else 0,
                "improved_accuracy": abs(improved_estimate - actual_tokens)
                / actual_tokens
                if actual_tokens > 0
                else 0,
                "simple_error": simple_estimate - actual_tokens,
                "improved_error": improved_estimate - actual_tokens,
            }
        )

        if logger:
            logger.info(
                f"Token estimation validation: "
                f"actual={actual_tokens}, "
                f"simple={simple_estimate} (error: {result['simple_error']:+d}), "
                f"improved={improved_estimate} (error: {result['improved_error']:+d})"
            )

    return result


class ChonkieTextChunker:
    """Local text chunker using Qwen3-optimized token estimation."""

    def __init__(self, chunk_size: int = 8000, chunk_overlap: int = 1000):
        """Initialize the local text chunker.

        Args:
            chunk_size: Maximum tokens per chunk
            chunk_overlap: Number of overlapping tokens between chunks
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

    def chunk_text(self, text: str) -> tuple[list[str], list[int]]:
        """Chunk text into smaller pieces with local token estimation.

        Args:
            text: Text to chunk

        Returns:
            tuple: (list of text chunks, list of token counts for each chunk)
        """
        if not text:
            return [], []

        # Estimate total tokens
        total_tokens = get_token_count(text)

        # If text is small enough, return as single chunk
        if total_tokens <= self.chunk_size:
            return [text], [total_tokens]

        # Calculate character-based chunk size (approximate)
        # Use conservative 3 chars per token as base ratio for chunking
        # This ensures we don't exceed token limits even with estimation variance
        chars_per_chunk = self.chunk_size * 3
        overlap_chars = self.chunk_overlap * 3

        chunks = []
        chunk_tokens = []
        start = 0

        while start < len(text):
            # Calculate end position
            end = min(start + chars_per_chunk, len(text))

            # Try to break at sentence boundaries
            if end < len(text):
                # Look for sentence endings within last 200 chars
                search_start = max(end - 200, start)
                for punct in ["。", "！", "？", ".", "!", "?", "\n\n"]:
                    punct_pos = text.rfind(punct, search_start, end)
                    if punct_pos > start:
                        end = punct_pos + 1
                        break

            chunk = text[start:end]
            chunks.append(chunk)
            chunk_tokens.append(get_token_count(chunk))

            # Move start position with overlap
            if end >= len(text):
                break
            start = max(start + 1, end - overlap_chars)

        return chunks, chunk_tokens

    def _fallback_chunk_text(self, text: str) -> tuple[list[str], list[int]]:
        """Simplified fallback chunking method."""
        # Simple character-based splitting as last resort
        # Use conservative 3 chars per token to match main chunking logic
        max_chars = self.chunk_size * 3  # Conservative estimate: 3 chars per token
        overlap_chars = self.chunk_overlap * 3

        chunks = []
        start = 0
        text_length = len(text)

        while start < text_length:
            end = min(start + max_chars, text_length)
            chunk = text[start:end]
            chunks.append(chunk)

            if end >= text_length:
                break

            start = max(start + 1, end - overlap_chars)

        # Use improved token counting for consistency
        chunk_tokens = []
        for chunk in chunks:
            try:
                # Use our improved token estimation
                tokens = get_token_count(chunk)
            except Exception:
                # Fallback to simple character estimation (conservative)
                tokens = max(1, len(chunk) // 3)
            chunk_tokens.append(tokens)
        return chunks, chunk_tokens

    def chunk_messages(
        self, messages: list, token_limit: int = None
    ) -> tuple[list[list], list[int]]:
        """Split messages into chunks while preserving message boundaries.

        Args:
            messages: List of message dictionaries with 'id', 'type', 'msg' fields
            token_limit: Maximum tokens per chunk (defaults to self.chunk_size)

        Returns:
            tuple: (list of message chunks, list of token counts for each chunk)
        """
        if not messages:
            return [], []

        if token_limit is None:
            token_limit = self.chunk_size

        # Use message-aware chunking instead of text-based chunking
        return self._chunk_messages_by_boundary(messages, token_limit)

    def _chunk_messages_by_boundary(
        self, messages: list, token_limit: int
    ) -> tuple[list[list], list[int]]:
        """Chunk messages by preserving message boundaries."""
        chunks = []
        chunk_tokens = []
        current_chunk = []
        current_tokens = 0

        for msg in messages:
            # Calculate tokens for this message
            formatted_msg = f"<|im_start|>{msg.get('type', 'UNKNOWN')}\n{msg.get('msg', '')}<|im_end|>"
            msg_tokens = get_token_count(formatted_msg)

            # Check if adding this message would exceed the limit
            if current_tokens + msg_tokens > token_limit and current_chunk:
                # Save current chunk
                chunks.append(current_chunk.copy())
                chunk_tokens.append(current_tokens)

                # Start new chunk
                current_chunk = [msg]
                current_tokens = msg_tokens
            else:
                # Add message to current chunk
                current_chunk.append(msg)
                current_tokens += msg_tokens

        # Add the last chunk if it has content
        if current_chunk:
            chunks.append(current_chunk)
            chunk_tokens.append(current_tokens)

        return chunks, chunk_tokens

    def _parse_chunk_to_messages(
        self, chunk_text: str, original_messages: list
    ) -> list:
        """Parse a text chunk back to message list by matching content."""
        chunk_messages = []

        # Split chunk by message boundaries
        parts = chunk_text.split("<|im_start|>")

        for part in parts:
            if not part.strip():
                continue

            # Extract message content
            if "<|im_end|>" in part:
                content_part = part.split("<|im_end|>")[0].strip()
            else:
                content_part = part.strip()

            # Parse message type and content
            lines = content_part.split("\n", 1)
            if len(lines) >= 2:
                msg_type = lines[0].strip()
                msg_content = lines[1].strip()

                # Find matching original message
                for orig_msg in original_messages:
                    if (
                        orig_msg.get("type") == msg_type
                        and orig_msg.get("msg") == msg_content
                    ):
                        chunk_messages.append(orig_msg)
                        break

        return chunk_messages


def timing_decorator(func):
    """Decorator to measure and log the execution time of both synchronous and asynchronous functions.

    For async functions, it properly awaits the result and measures the real execution time.
    """
    logger = get_logger(module_name="timing")

    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        elapsed_time = end_time - start_time
        logger.info(
            f"Function '{func.__name__}' executed in {elapsed_time:.2f} seconds"
        )
        return result

    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        result = await func(*args, **kwargs)
        end_time = time.time()
        elapsed_time = end_time - start_time
        logger.info(
            f"Async function '{func.__name__}' executed in {elapsed_time:.2f} seconds"
        )
        return result

    if inspect.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper
